// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: '', // 后端API地址（动态设置）
    wsUrl: '', // WebSocket地址（动态设置）
    amapKey: 'your_amap_key_here', // 高德地图小程序SDK密钥
    debug: false // 调试模式
  },

  onLaunch() {
    console.log('小程序启动')

    // 初始化环境配置
    this.initEnvironment()

    // 性能监控
    this.performanceMonitor()

    // 检查登录状态
    this.checkLoginStatus()

    // 获取系统信息
    this.getSystemInfo()

    // 预加载关键数据
    this.preloadData()

    // 检查更新
    this.checkForUpdate()
  },

  // 初始化环境配置
  initEnvironment() {
    // 获取小程序账号信息
    const accountInfo = wx.getAccountInfoSync()
    const envVersion = accountInfo.miniProgram.envVersion

    console.log('小程序环境:', envVersion)

    // 根据环境设置API地址
    if (envVersion === 'develop' || envVersion === 'trial') {
      // 开发环境或体验版
      this.globalData.baseUrl = 'http://localhost:8080'
      this.globalData.wsUrl = 'ws://localhost:8081/ws'
      this.globalData.debug = true
      console.log('使用开发环境配置')
    } else {
      // 正式版
      this.globalData.baseUrl = 'https://api.beijingnavigation.com'
      this.globalData.wsUrl = 'wss://api.beijingnavigation.com/ws'
      this.globalData.debug = false
      console.log('使用生产环境配置')
    }

    console.log('API地址:', this.globalData.baseUrl)
    console.log('WebSocket地址:', this.globalData.wsUrl)
  },

  onShow() {
    console.log('小程序显示')
  },

  onHide() {
    console.log('小程序隐藏')
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    if (token) {
      this.globalData.token = token
      // 验证token有效性
      this.validateToken(token)
    }
  },

  // 验证token有效性
  validateToken(token) {
    wx.request({
      url: `${this.globalData.baseUrl}/api/v1/auth/validate`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200) {
          this.globalData.userInfo = res.data.user
        } else {
          // token无效，清除本地存储
          this.clearLoginInfo()
        }
      },
      fail: () => {
        this.clearLoginInfo()
      }
    })
  },

  // 清除登录信息
  clearLoginInfo() {
    this.globalData.token = null
    this.globalData.userInfo = null
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        console.log('系统信息:', res)
      }
    })
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    })
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading()
  },

  // 显示消息提示
  showToast(title, icon = 'none', duration = 2000) {
    wx.showToast({
      title: title,
      icon: icon,
      duration: duration
    })
  },

  // 网络请求封装
  request(options) {
    const { url, method = 'GET', data = {}, header = {} } = options

    // 添加认证头
    if (this.globalData.token) {
      header['Authorization'] = `Bearer ${this.globalData.token}`
    }

    header['Content-Type'] = 'application/json'

    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.globalData.baseUrl}${url}`,
        method: method,
        data: data,
        header: header,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else if (res.statusCode === 401) {
            // 未授权，跳转到登录页
            this.clearLoginInfo()
            wx.navigateTo({
              url: '/pages/login/login'
            })
            reject(new Error('未授权'))
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  },

  // 检查用户权限
  checkUserPermission(feature) {
    return new Promise((resolve) => {
      if (!this.globalData.token) {
        resolve({ hasPermission: false, reason: 'not_logged_in' })
        return
      }

      this.request({
        url: '/api/v1/users/subscription',
        method: 'GET'
      }).then((data) => {
        const subscription = data.subscription
        if (!subscription) {
          resolve({ hasPermission: false, reason: 'no_subscription' })
          return
        }

        const now = new Date()

        // 试用期用户权限检查
        if (subscription.type === 'trial') {
          const trialExpiry = new Date(subscription.trial_expiry)
          if (now < trialExpiry) {
            resolve({ hasPermission: true, subscription: subscription })
          } else {
            resolve({ hasPermission: false, reason: 'trial_expired', subscription: subscription })
          }
          return
        }

        // 高级版用户权限检查
        if (subscription.type === 'premium') {
          resolve({ hasPermission: true, subscription: subscription })
          return
        }

        // 免费用户权限检查
        if (feature === 'view_checkpoints') {
          resolve({ hasPermission: true, subscription: subscription })
        } else {
          resolve({ hasPermission: false, reason: 'free_user_limitation', subscription: subscription })
        }
      }).catch(() => {
        resolve({ hasPermission: false, reason: 'network_error' })
      })
    })
  },

  // 显示权限提示
  showPermissionDialog(reason, subscription) {
    let title = '功能受限'
    let content = ''
    let confirmText = '我知道了'
    let showUpgrade = false

    switch (reason) {
      case 'not_logged_in':
        title = '登录提示'
        content = '请先登录后使用此功能'
        confirmText = '去登录'
        break
      case 'trial_expired':
        content = '您的试用期已过期。升级高级版继续使用完整功能。'
        confirmText = '升级会员'
        showUpgrade = true
        break
      case 'free_user_limitation':
        content = '此功能仅限试用期和高级版用户使用。免费用户只能查看检查站信息。'
        confirmText = '升级会员'
        showUpgrade = true
        break
      case 'network_error':
        title = '网络错误'
        content = '网络连接失败，请检查网络设置后重试'
        break
      default:
        content = '暂无权限使用此功能'
    }

    wx.showModal({
      title: title,
      content: content,
      confirmText: confirmText,
      cancelText: showUpgrade ? '稍后再说' : '取消',
      success: (res) => {
        if (res.confirm) {
          if (reason === 'not_logged_in') {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          } else if (showUpgrade) {
            wx.switchTab({
              url: '/pages/profile/profile'
            })
          }
        }
      }
    })
  },

  // 强制联网验证
  checkNetworkConnection() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            wx.showModal({
              title: '网络连接失败',
              content: '此功能需要网络连接，请检查网络设置',
              showCancel: false,
              confirmText: '我知道了'
            })
            resolve(false)
          } else {
            resolve(true)
          }
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  },

  // 性能监控
  performanceMonitor() {
    // 监控小程序启动性能
    const launchTime = Date.now()
    this.globalData.launchTime = launchTime

    // 监控内存使用
    if (wx.onMemoryWarning) {
      wx.onMemoryWarning(() => {
        console.warn('内存不足警告')
        this.cleanupMemory()
      })
    }

    // 监控网络状态变化
    wx.onNetworkStatusChange((res) => {
      console.log('网络状态变化:', res)
      this.globalData.networkType = res.networkType
      this.globalData.isConnected = res.isConnected
    })
  },

  // 内存清理
  cleanupMemory() {
    // 清理缓存数据
    try {
      const cacheKeys = ['checkpoints_cache', 'routes_cache', 'temp_data']
      cacheKeys.forEach(key => {
        wx.removeStorageSync(key)
      })
      console.log('内存清理完成')
    } catch (e) {
      console.error('内存清理失败:', e)
    }
  },

  // 预加载关键数据
  preloadData() {
    // 预加载检查站数据（如果用户已登录）
    if (this.globalData.token) {
      this.preloadCheckpoints()
    }

    // 预加载用户偏好设置
    this.preloadUserPreferences()
  },

  // 预加载检查站数据
  preloadCheckpoints() {
    // 检查缓存是否存在且未过期
    const cacheKey = 'checkpoints_cache'
    const cacheTime = wx.getStorageSync(`${cacheKey}_time`)
    const now = Date.now()

    // 缓存有效期5分钟
    if (cacheTime && (now - cacheTime) < 5 * 60 * 1000) {
      return
    }

    // 静默加载检查站数据
    this.request({
      url: '/api/v1/checkpoints',
      method: 'GET',
      data: { page: 1, pageSize: 50 }
    }).then((data) => {
      // 缓存数据
      wx.setStorageSync(cacheKey, data.checkpoints)
      wx.setStorageSync(`${cacheKey}_time`, now)
      console.log('检查站数据预加载完成')
    }).catch((err) => {
      console.log('检查站数据预加载失败:', err)
    })
  },

  // 预加载用户偏好设置
  preloadUserPreferences() {
    const defaultPreferences = {
      defaultAvoidLevel: 2,
      autoUpdate: true,
      voiceNavigation: true,
      nightMode: false,
      mapStyle: 'standard'
    }

    let preferences = wx.getStorageSync('user_preferences')
    if (!preferences) {
      preferences = defaultPreferences
      wx.setStorageSync('user_preferences', preferences)
    }

    this.globalData.userPreferences = preferences
  },

  // 检查更新
  checkForUpdate() {
    if (wx.getUpdateManager) {
      const updateManager = wx.getUpdateManager()

      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果:', res.hasUpdate)
      })

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已下载完成，是否立即重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败')
      })
    }
  },

  // 数据统计上报
  reportAnalytics(event, data = {}) {
    // 这里可以集成第三方统计SDK
    console.log('统计上报:', event, data)

    // 简单的本地统计
    const analytics = wx.getStorageSync('app_analytics') || {}
    const today = new Date().toDateString()

    if (!analytics[today]) {
      analytics[today] = {}
    }

    if (!analytics[today][event]) {
      analytics[today][event] = 0
    }

    analytics[today][event]++

    // 只保留最近7天的数据
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    Object.keys(analytics).forEach(date => {
      if (new Date(date) < sevenDaysAgo) {
        delete analytics[date]
      }
    })

    wx.setStorageSync('app_analytics', analytics)
  },

  // 错误上报
  reportError(error, context = '') {
    console.error('错误上报:', error, context)

    // 这里可以集成错误监控服务
    const errorLog = {
      error: error.toString(),
      context: context,
      timestamp: new Date().toISOString(),
      userAgent: this.globalData.systemInfo?.system || 'unknown',
      version: this.globalData.systemInfo?.version || 'unknown'
    }

    // 本地存储错误日志（最多保存10条）
    let errorLogs = wx.getStorageSync('error_logs') || []
    errorLogs.unshift(errorLog)
    errorLogs = errorLogs.slice(0, 10)
    wx.setStorageSync('error_logs', errorLogs)
  }
})