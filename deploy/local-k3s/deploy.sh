#!/bin/bash

# k3s 部署脚本
set -e

echo "🚀 部署应用到 k3s..."

# 切换到脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查 k3s 是否运行
if ! systemctl is-active --quiet k3s; then
    echo "❌ k3s 未运行，请先安装: ./install-k3s.sh"
    exit 1
fi

# 检查 kubectl
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl 未找到"
    exit 1
fi

# 应用 Kubernetes 资源
echo "📦 应用 Kubernetes 资源..."

# 创建命名空间
kubectl apply -f manifests/namespace.yaml

# 应用配置
kubectl apply -f manifests/configmap.yaml
kubectl apply -f manifests/secret.yaml

# 部署数据库
kubectl apply -f manifests/postgres.yaml
kubectl apply -f manifests/redis.yaml

# 等待数据库就绪
echo "⏳ 等待数据库就绪..."
kubectl wait --for=condition=ready pod -l app=postgres -n app --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n app --timeout=300s

# 部署应用
kubectl apply -f manifests/deployment.yaml
kubectl apply -f manifests/service.yaml

# 等待应用就绪
echo "⏳ 等待应用就绪..."
kubectl wait --for=condition=available deployment/app -n app --timeout=300s

echo "✅ 部署完成！"
echo ""
echo "📋 访问信息："
echo "  - 端口转发: kubectl port-forward -n app svc/app-service 8080:8080"
echo "  - 本地访问: http://localhost:8080"
echo ""
echo "📊 管理命令："
echo "  - 查看状态: kubectl get all -n app"
echo "  - 查看日志: kubectl logs -f -l app=app -n app"
echo "  - 删除部署: kubectl delete namespace app"